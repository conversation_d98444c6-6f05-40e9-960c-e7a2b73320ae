/**
 * Advanced Content Security Policy Middleware
 * Implements enhanced CSP with nonce-based script execution and strict security rules
 */

import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { SecurityMonitor } from '../utils/securityMonitor';
import { AuthEventType, SecurityLevel } from '../utils/authLogger';

interface CSPRequest extends Request {
  nonce?: string;
  cspViolation?: boolean;
}

interface CSPConfig {
  enableNonce: boolean;
  strictMode: boolean;
  reportOnly: boolean;
  reportUri?: string;
  allowedDomains: string[];
  trustedScriptSources: string[];
  trustedStyleSources: string[];
  allowInlineStyles: boolean;
  allowInlineScripts: boolean;
  allowEval: boolean;
  upgradeInsecureRequests: boolean;
}

class AdvancedCSPService {
  private static readonly DEFAULT_CONFIG: CSPConfig = {
    enableNonce: true,
    strictMode: true,
    reportOnly: false,
    allowedDomains: ['self'],
    trustedScriptSources: ['self'],
    trustedStyleSources: ['self', 'unsafe-inline'],
    allowInlineStyles: false,
    allowInlineScripts: false,
    allowEval: false,
    upgradeInsecureRequests: true
  };

  /**
   * Generate cryptographically secure nonce
   */
  static generateNonce(): string {
    return crypto.randomBytes(16).toString('base64');
  }

  /**
   * Create CSP middleware with custom configuration
   */
  static createCSPMiddleware(config: Partial<CSPConfig> = {}) {
    const finalConfig = { ...AdvancedCSPService.DEFAULT_CONFIG, ...config };

    return (req: CSPRequest, res: Response, next: NextFunction) => {
      try {
        // Generate nonce for this request
        if (finalConfig.enableNonce) {
          req.nonce = AdvancedCSPService.generateNonce();
        }

        // Build CSP header
        const cspHeader = AdvancedCSPService.buildCSPHeader(finalConfig, req.nonce);

        // Set CSP header
        const headerName = finalConfig.reportOnly ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';
        res.setHeader(headerName, cspHeader);

        // Add additional security headers
        AdvancedCSPService.setAdditionalSecurityHeaders(res, finalConfig);

        // Log CSP policy application
        AdvancedCSPService.logCSPApplication(req, finalConfig);

        next();
      } catch (error) {
        console.error('CSP middleware error:', error);
        // Continue without CSP on error to avoid breaking the application
        next();
      }
    };
  }

  /**
   * Build comprehensive CSP header
   */
  private static buildCSPHeader(config: CSPConfig, nonce?: string): string {
    const directives: string[] = [];

    // Default source directive
    directives.push(`default-src ${config.allowedDomains.map(d => `'${d}'`).join(' ')}`);

    // Script source directive with nonce support
    const scriptSources = [...config.trustedScriptSources.map(s => `'${s}'`)];
    if (config.enableNonce && nonce) {
      scriptSources.push(`'nonce-${nonce}'`);
    }
    if (config.allowInlineScripts) {
      scriptSources.push("'unsafe-inline'");
    }
    if (config.allowEval) {
      scriptSources.push("'unsafe-eval'");
    }
    directives.push(`script-src ${scriptSources.join(' ')}`);

    // Style source directive
    const styleSources = [...config.trustedStyleSources.map(s => `'${s}'`)];
    if (config.allowInlineStyles) {
      styleSources.push("'unsafe-inline'");
    }
    if (config.enableNonce && nonce) {
      styleSources.push(`'nonce-${nonce}'`);
    }
    directives.push(`style-src ${styleSources.join(' ')}`);

    // Object source directive (strict)
    directives.push("object-src 'none'");

    // Base URI directive
    directives.push("base-uri 'self'");

    // Form action directive
    directives.push("form-action 'self'");

    // Frame ancestors directive (prevent clickjacking)
    directives.push("frame-ancestors 'none'");

    // Image source directive
    directives.push("img-src 'self' data: https:");

    // Font source directive
    directives.push("font-src 'self' https:");

    // Connect source directive (for AJAX, WebSocket, etc.)
    directives.push("connect-src 'self' https:");

    // Media source directive
    directives.push("media-src 'self'");

    // Worker source directive
    directives.push("worker-src 'self'");

    // Manifest source directive
    directives.push("manifest-src 'self'");

    // Frame source directive
    directives.push("frame-src 'none'");

    // Upgrade insecure requests
    if (config.upgradeInsecureRequests) {
      directives.push("upgrade-insecure-requests");
    }

    // Block all mixed content
    directives.push("block-all-mixed-content");

    // Report URI for violations
    if (config.reportUri) {
      directives.push(`report-uri ${config.reportUri}`);
    }

    return directives.join('; ');
  }

  /**
   * Set additional security headers
   */
  private static setAdditionalSecurityHeaders(res: Response, config: CSPConfig): void {
    // X-Content-Type-Options
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // X-Frame-Options (backup for older browsers)
    res.setHeader('X-Frame-Options', 'DENY');

    // X-XSS-Protection (for older browsers)
    res.setHeader('X-XSS-Protection', '1; mode=block');

    // Referrer Policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

    // Permissions Policy (formerly Feature Policy)
    const permissionsPolicy = [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()',
      'magnetometer=()',
      'accelerometer=()',
      'gyroscope=()'
    ].join(', ');
    res.setHeader('Permissions-Policy', permissionsPolicy);

    // Cross-Origin Embedder Policy
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');

    // Cross-Origin Opener Policy
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');

    // Cross-Origin Resource Policy
    res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');

    // Strict Transport Security (if HTTPS)
    if (config.upgradeInsecureRequests) {
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
  }

  /**
   * Log CSP policy application
   */
  private static async logCSPApplication(req: CSPRequest, config: CSPConfig): Promise<void> {
    try {
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.CONFIGURATION_CHANGE,
        message: 'CSP policy applied',
        email: req.user?.email || '',
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: SecurityLevel.INFO,
        sessionId: req.sessionID,
        details: {
          endpoint: req.originalUrl,
          method: req.method,
          cspConfig: {
            strictMode: config.strictMode,
            enableNonce: config.enableNonce,
            reportOnly: config.reportOnly
          },
          nonceGenerated: !!req.nonce
        }
      });
    } catch (error) {
      console.error('Error logging CSP application:', error);
    }
  }

  /**
   * CSP violation report handler
   */
  static cspViolationHandler = async (req: Request, res: Response) => {
    try {
      const violation = req.body;

      // Log CSP violation
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.SUSPICIOUS_ACTIVITY,
        message: 'CSP violation detected',
        email: req.user?.email || '',
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: SecurityLevel.WARNING,
        sessionId: req.sessionID,
        riskScore: 60,
        details: {
          violation: {
            documentUri: violation['document-uri'],
            referrer: violation.referrer,
            violatedDirective: violation['violated-directive'],
            effectiveDirective: violation['effective-directive'],
            originalPolicy: violation['original-policy'],
            blockedUri: violation['blocked-uri'],
            statusCode: violation['status-code'],
            sourceFile: violation['source-file'],
            lineNumber: violation['line-number'],
            columnNumber: violation['column-number']
          }
        }
      });

      // Check for potential XSS attempts
      const suspiciousPatterns = [
        'javascript:',
        'data:text/html',
        'vbscript:',
        'onload=',
        'onerror=',
        'onclick=',
        '<script',
        'eval(',
        'setTimeout(',
        'setInterval('
      ];

      const blockedUri = violation['blocked-uri'] || '';
      const sourceFile = violation['source-file'] || '';
      
      const isSuspicious = suspiciousPatterns.some(pattern => 
        blockedUri.toLowerCase().includes(pattern) || 
        sourceFile.toLowerCase().includes(pattern)
      );

      if (isSuspicious) {
        await SecurityMonitor.logSecurityEvent({
          eventType: AuthEventType.SUSPICIOUS_ACTIVITY,
          message: 'Potential XSS attempt detected via CSP violation',
          email: req.user?.email || '',
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent'),
          severity: SecurityLevel.HIGH,
          sessionId: req.sessionID,
          riskScore: 85,
          details: {
            suspiciousContent: {
              blockedUri,
              sourceFile,
              violatedDirective: violation['violated-directive']
            }
          }
        });
      }

      res.status(204).send(); // No content response
    } catch (error) {
      console.error('CSP violation handler error:', error);
      res.status(500).json({ error: 'Failed to process CSP violation report' });
    }
  };

  /**
   * Predefined CSP configurations for different environments
   */
  static readonly configurations = {
    // Strict configuration for production
    production: {
      enableNonce: true,
      strictMode: true,
      reportOnly: false,
      allowedDomains: ['self'],
      trustedScriptSources: ['self'],
      trustedStyleSources: ['self'],
      allowInlineStyles: false,
      allowInlineScripts: false,
      allowEval: false,
      upgradeInsecureRequests: true,
      reportUri: '/api/security/csp-violation'
    },

    // Development configuration with relaxed rules
    development: {
      enableNonce: true,
      strictMode: false,
      reportOnly: true,
      allowedDomains: ['self', 'localhost:*'],
      trustedScriptSources: ['self', 'localhost:*'],
      trustedStyleSources: ['self', 'unsafe-inline', 'localhost:*'],
      allowInlineStyles: true,
      allowInlineScripts: false,
      allowEval: true,
      upgradeInsecureRequests: false,
      reportUri: '/api/security/csp-violation'
    },

    // Testing configuration
    testing: {
      enableNonce: false,
      strictMode: false,
      reportOnly: true,
      allowedDomains: ['self', '*'],
      trustedScriptSources: ['self', 'unsafe-inline', 'unsafe-eval'],
      trustedStyleSources: ['self', 'unsafe-inline'],
      allowInlineStyles: true,
      allowInlineScripts: true,
      allowEval: true,
      upgradeInsecureRequests: false
    }
  };
}

/**
 * Helper function to get nonce from request
 */
export function getNonce(req: CSPRequest): string | undefined {
  return req.nonce;
}

/**
 * Middleware factory for different environments
 */
export function createCSP(environment: 'production' | 'development' | 'testing' = 'production') {
  const config = AdvancedCSPService.configurations[environment];
  return AdvancedCSPService.createCSPMiddleware(config);
}

export { AdvancedCSPService };
export default AdvancedCSPService.createCSPMiddleware;
