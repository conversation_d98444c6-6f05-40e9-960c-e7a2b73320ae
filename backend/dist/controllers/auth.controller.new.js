"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyAuth = exports.changePassword = exports.getCurrentAdmin = exports.adminLogout = exports.adminLogin = exports.aggressiveLimiter = exports.loginLimiter = void 0;
const express_validator_1 = require("express-validator");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const Admin_1 = require("../models/Admin");
const express_rate_limit_1 = require("express-rate-limit");
const database_1 = require("../config/database");
const authLogger_1 = require("../utils/authLogger");
const passwordPolicy_1 = require("../utils/passwordPolicy");
const securityMonitor_1 = require("../utils/securityMonitor");
const geolocation_1 = require("../utils/geolocation");
// PROFESSIONAL-GRADE RATE LIMITING
exports.loginLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 3, // Only 3 attempts per window (stricter)
    message: {
        success: false,
        message: 'Too many login attempts from this IP',
        error: 'Account temporarily locked. Try again after 15 minutes or contact support.',
        code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Skip successful requests
    skipSuccessfulRequests: true,
    // Custom key generator for IP-based limiting
    keyGenerator: (req) => {
        return req.ip || req.connection.remoteAddress || 'unknown';
    },
    // Custom handler for rate limit exceeded
    handler: (req, res) => {
        console.warn(`Rate limit exceeded for IP: ${req.ip} at ${new Date().toISOString()}`);
        res.status(429).json({
            success: false,
            message: 'Too many login attempts from this IP address',
            error: 'Your IP has been temporarily blocked due to suspicious activity. Please try again later or contact support.',
            code: 'RATE_LIMIT_EXCEEDED',
            retryAfter: Math.ceil(15 * 60) // 15 minutes in seconds
        });
    }
});
// Aggressive rate limiting for repeated failures
exports.aggressiveLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // 10 attempts per hour across all endpoints
    message: {
        success: false,
        message: 'Suspicious activity detected',
        error: 'Your IP has been blocked due to suspicious activity. Contact support if this is an error.',
        code: 'SUSPICIOUS_ACTIVITY'
    },
    standardHeaders: true,
    legacyHeaders: false,
});
/**
 * Admin login with HTTP-only cookies
 */
const adminLogin = async (req, res) => {
    var _a;
    try {
        // Validate request
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const { email, password } = req.body;
        const ip = req.ip || 'unknown';
        const userAgent = req.get('User-Agent') || '';
        const sessionId = req.sessionID || `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        // ENTERPRISE-GRADE SECURITY MONITORING
        // Generate device fingerprint
        const deviceFingerprint = securityMonitor_1.SecurityMonitor.generateDeviceFingerprint(req);
        const deviceInfo = securityMonitor_1.SecurityMonitor.parseDeviceInfo(userAgent);
        // Get geolocation data
        const geolocation = await geolocation_1.GeolocationService.getGeolocation(ip);
        const ipRiskScore = await geolocation_1.GeolocationService.assessIPRisk(ip);
        // Find admin by email
        const admin = await Admin_1.Admin.findByEmail(email);
        if (!admin) {
            // Log failed login attempt for non-existent user
            await securityMonitor_1.SecurityMonitor.logLoginAttempt({
                email,
                ip,
                userAgent,
                success: false,
                failureReason: 'USER_NOT_FOUND',
                geolocation,
                deviceFingerprint,
                sessionId
            });
            // Log security event
            await securityMonitor_1.SecurityMonitor.logSecurityEvent({
                eventType: authLogger_1.AuthEventType.LOGIN_FAILED,
                message: 'Login attempt for non-existent admin account',
                email,
                ip,
                userAgent,
                severity: authLogger_1.SecurityLevel.WARNING,
                sessionId,
                geolocation,
                deviceFingerprint,
                riskScore: ipRiskScore + 20, // Higher risk for non-existent accounts
                details: { reason: 'USER_NOT_FOUND' }
            });
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }
        // Check if account is locked
        if (admin.lockUntil && admin.lockUntil > new Date()) {
            return res.status(423).json({
                success: false,
                message: 'Account is locked',
                error: 'Please try again later or contact support'
            });
        }
        // Verify password
        if (!admin.password) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }
        const isValidPassword = await bcryptjs_1.default.compare(password, admin.password);
        if (!isValidPassword) {
            // PROFESSIONAL PROGRESSIVE ACCOUNT LOCKOUT
            const newAttempts = (admin.failedLoginAttempts || 0) + 1;
            let lockUntil;
            // Progressive lockout periods
            if (newAttempts >= 3 && newAttempts < 5) {
                lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
            }
            else if (newAttempts >= 5 && newAttempts < 10) {
                lockUntil = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
            }
            else if (newAttempts >= 10) {
                lockUntil = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
            }
            await Admin_1.Admin.updateFailedLoginAttempts(admin.id, newAttempts, lockUntil);
            // Log failed login attempt
            await securityMonitor_1.SecurityMonitor.logLoginAttempt({
                email,
                ip,
                userAgent,
                success: false,
                failureReason: 'INVALID_PASSWORD',
                geolocation,
                deviceFingerprint,
                sessionId
            });
            // Log comprehensive security event
            await securityMonitor_1.SecurityMonitor.logSecurityEvent({
                eventType: authLogger_1.AuthEventType.LOGIN_FAILED,
                message: `Failed login attempt ${newAttempts} - Invalid password`,
                adminId: admin.id,
                email,
                ip,
                userAgent,
                severity: newAttempts >= 5 ? authLogger_1.SecurityLevel.WARNING : authLogger_1.SecurityLevel.INFO,
                sessionId,
                geolocation,
                deviceFingerprint,
                riskScore: ipRiskScore + (newAttempts * 5), // Increase risk with attempts
                details: {
                    reason: 'INVALID_PASSWORD',
                    attempts: newAttempts,
                    locked: !!lockUntil,
                    lockUntil: lockUntil === null || lockUntil === void 0 ? void 0 : lockUntil.toISOString()
                }
            });
            // Generic error message to prevent user enumeration
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials',
                code: 'AUTHENTICATION_FAILED'
            });
        }
        // ENTERPRISE-GRADE PASSWORD SECURITY CHECKS
        // Check if password has expired
        const passwordExpired = await (0, passwordPolicy_1.hasPasswordExpired)(admin.id, true);
        if (passwordExpired) {
            await (0, authLogger_1.logAuthEvent)(authLogger_1.AuthEventType.PASSWORD_EXPIRED, 'Login blocked - password expired', { adminId: admin.id, email }, authLogger_1.SecurityLevel.WARNING);
            return res.status(403).json({
                success: false,
                message: 'Password has expired',
                error: 'Please reset your password to continue',
                code: 'PASSWORD_EXPIRED',
                requirePasswordReset: true
            });
        }
        // Check if password change is required
        if (admin.mustChangePassword) {
            await (0, authLogger_1.logAuthEvent)(authLogger_1.AuthEventType.PASSWORD_CHANGE_REQUIRED, 'Login blocked - password change required', { adminId: admin.id, email }, authLogger_1.SecurityLevel.INFO);
            return res.status(403).json({
                success: false,
                message: 'Password change required',
                error: 'You must change your password before continuing',
                code: 'PASSWORD_CHANGE_REQUIRED',
                requirePasswordChange: true
            });
        }
        // Check days until password expires (warn if < 7 days)
        const daysUntilExpiry = await (0, passwordPolicy_1.getDaysUntilPasswordExpires)(admin.id, true);
        const passwordExpiryWarning = daysUntilExpiry !== null && daysUntilExpiry <= 7 && daysUntilExpiry > 0;
        // ENTERPRISE-GRADE TRAVEL ANALYSIS & DEVICE TRACKING
        // Get previous login location for travel analysis
        let travelAnalysis = null;
        let travelRiskScore = 0;
        try {
            const previousLoginResult = await (0, database_1.query)(`
        SELECT geolocation, timestamp FROM security_events
        WHERE admin_id = $1 AND event_type = $2
        ORDER BY timestamp DESC
        LIMIT 1 OFFSET 1
      `, [admin.id, authLogger_1.AuthEventType.LOGIN_SUCCESS]);
            if (previousLoginResult.rows.length > 0 && geolocation) {
                const previousLogin = previousLoginResult.rows[0];
                const previousGeolocation = previousLogin.geolocation;
                const timeDiff = (new Date().getTime() - new Date(previousLogin.timestamp).getTime()) / (1000 * 60); // minutes
                if (previousGeolocation && previousGeolocation.lat && previousGeolocation.lon) {
                    travelAnalysis = geolocation_1.GeolocationService.analyzeTravelPattern(previousGeolocation, geolocation, timeDiff);
                    travelRiskScore = travelAnalysis.riskScore;
                    // Create alert for impossible travel
                    if (travelAnalysis.isImpossibleTravel) {
                        await securityMonitor_1.SecurityMonitor.createSecurityAlert({
                            alertType: 'IMPOSSIBLE_TRAVEL',
                            title: 'Impossible Travel Detected',
                            description: `Admin ${email} logged in from ${geolocation.city}, ${geolocation.country} but was previously in ${previousGeolocation.city}, ${previousGeolocation.country} ${Math.round(timeDiff)} minutes ago. Required speed: ${Math.round(travelAnalysis.maxPossibleSpeed || 0)} km/h`,
                            severity: 'high',
                            adminId: admin.id,
                            ip,
                            metadata: { travelAnalysis }
                        });
                    }
                }
            }
        }
        catch (travelError) {
            console.error('Error analyzing travel patterns:', travelError);
        }
        // Track device
        await securityMonitor_1.SecurityMonitor.trackDevice(undefined, // userId
        admin.id, // adminId
        deviceInfo, ip, false // not automatically trusted
        );
        // Calculate total risk score
        const totalRiskScore = Math.min(100, ipRiskScore + travelRiskScore);
        // Log successful login attempt
        await securityMonitor_1.SecurityMonitor.logLoginAttempt({
            email,
            ip,
            userAgent,
            success: true,
            geolocation,
            deviceFingerprint,
            sessionId
        });
        // Reset failed login attempts and update last login
        await Admin_1.Admin.updateLastLogin(admin.id);
        // Log comprehensive successful login event
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.LOGIN_SUCCESS,
            message: 'Admin login successful',
            adminId: admin.id,
            email,
            ip,
            userAgent,
            severity: totalRiskScore > securityMonitor_1.RiskLevel.MEDIUM ? authLogger_1.SecurityLevel.WARNING : authLogger_1.SecurityLevel.INFO,
            sessionId,
            geolocation,
            deviceFingerprint,
            riskScore: totalRiskScore,
            details: {
                passwordExpiryWarning,
                daysUntilExpiry,
                travelAnalysis,
                deviceInfo,
                ipRiskScore,
                travelRiskScore
            }
        });
        // PROFESSIONAL JWT TOKEN GENERATION with security
        const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
        const jwtOptions = {
            expiresIn: '2h', // Fixed string value
            issuer: 'mabourse-api',
            audience: 'mabourse-admin'
        };
        const token = jsonwebtoken_1.default.sign({
            id: admin.id,
            email: admin.email,
            role: admin.role,
            isMainAdmin: admin.isMainAdmin,
            // Security additions
            jti: `${admin.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // Unique token ID
            ip: req.ip || req.connection.remoteAddress, // IP binding for additional security
            userAgent: (_a = req.get('User-Agent')) === null || _a === void 0 ? void 0 : _a.substring(0, 100) // User agent binding (truncated)
        }, jwtSecret, jwtOptions);
        // PROFESSIONAL-GRADE SECURE COOKIE CONFIGURATION
        res.cookie('auth_token', token, {
            httpOnly: true, // Prevents XSS attacks
            secure: process.env.NODE_ENV === 'production', // HTTPS only in production
            sameSite: 'strict', // CSRF protection - strict for admin portal
            maxAge: 2 * 60 * 60 * 1000, // 2 hours (matches JWT expiration)
            path: '/', // Cookie path
            domain: process.env.NODE_ENV === 'production' ? process.env.COOKIE_DOMAIN : undefined,
            // Additional security flags
            priority: 'high' // High priority cookie
        });
        // Log successful login for security monitoring
        console.log(`Successful admin login: ${admin.email} from IP: ${req.ip} at ${new Date().toISOString()}`);
        // Parse privileges
        const privileges = typeof admin.privileges === 'string'
            ? JSON.parse(admin.privileges)
            : admin.privileges;
        // Return success response with security information
        const response = {
            success: true,
            message: 'Login successful',
            data: {
                admin: {
                    id: admin.id,
                    name: admin.name,
                    email: admin.email,
                    role: admin.role,
                    isMainAdmin: admin.isMainAdmin,
                    privileges,
                    twoFactorEnabled: admin.twoFactorEnabled || false
                }
            }
        };
        // Add password expiry warning if applicable
        if (passwordExpiryWarning && daysUntilExpiry !== null) {
            response.warning = {
                type: 'PASSWORD_EXPIRY',
                message: `Your password will expire in ${daysUntilExpiry} day${daysUntilExpiry === 1 ? '' : 's'}`,
                daysRemaining: daysUntilExpiry,
                action: 'Please change your password soon to avoid account lockout'
            };
        }
        res.json(response);
    }
    catch (error) {
        console.error('Admin login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};
exports.adminLogin = adminLogin;
/**
 * Admin logout
 */
const adminLogout = async (req, res) => {
    try {
        // Clear the authentication cookie
        res.clearCookie('auth_token', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            path: '/'
        });
        res.json({
            success: true,
            message: 'Logout successful'
        });
    }
    catch (error) {
        console.error('Admin logout error:', error);
        res.status(500).json({
            success: false,
            message: 'Logout failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};
exports.adminLogout = adminLogout;
/**
 * Get current admin profile
 */
const getCurrentAdmin = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return res.status(404).json({
                success: false,
                message: 'Admin not found'
            });
        }
        // Parse privileges
        const privileges = typeof admin.privileges === 'string'
            ? JSON.parse(admin.privileges)
            : admin.privileges;
        res.json({
            success: true,
            message: 'Admin profile retrieved successfully',
            data: {
                admin: {
                    id: admin.id,
                    name: admin.name,
                    email: admin.email,
                    role: admin.role,
                    isMainAdmin: admin.isMainAdmin,
                    privileges,
                    twoFactorEnabled: admin.twoFactorEnabled || false,
                    lastLogin: admin.lastLogin,
                    createdAt: admin.createdAt,
                    updatedAt: admin.updatedAt
                }
            }
        });
    }
    catch (error) {
        console.error('Get admin profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve admin profile',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};
exports.getCurrentAdmin = getCurrentAdmin;
/**
 * Change admin password with enterprise-grade security
 */
const changePassword = async (req, res) => {
    var _a, _b;
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }
        const { currentPassword, newPassword } = req.body;
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({
                success: false,
                message: 'Not authenticated'
            });
        }
        // Get admin from database
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return res.status(404).json({
                success: false,
                message: 'Admin not found'
            });
        }
        // Verify current password
        const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, admin.password);
        if (!isCurrentPasswordValid) {
            await (0, authLogger_1.logAuthEvent)(authLogger_1.AuthEventType.PASSWORD_CHANGE_FAILED, 'Password change failed - invalid current password', { adminId, email: admin.email }, authLogger_1.SecurityLevel.WARNING);
            return res.status(400).json({
                success: false,
                message: 'Current password is incorrect'
            });
        }
        // Validate new password strength
        const passwordValidation = (0, passwordPolicy_1.validatePasswordStrength)(newPassword);
        if (!passwordValidation.valid) {
            return res.status(400).json({
                success: false,
                message: 'New password does not meet security requirements',
                error: passwordValidation.message,
                suggestions: passwordValidation.suggestions,
                score: passwordValidation.score
            });
        }
        // Check if new password is in history
        const isPasswordReused = await (0, passwordPolicy_1.isPasswordInHistory)(adminId, newPassword, true);
        if (isPasswordReused) {
            return res.status(400).json({
                success: false,
                message: 'Cannot reuse a recent password',
                error: 'Please choose a password you have not used recently'
            });
        }
        // Hash new password
        const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, 12);
        const passwordExpiresAt = new Date(Date.now() + (90 * 24 * 60 * 60 * 1000)); // 90 days
        // Update admin password
        await Admin_1.Admin.update(adminId, {
            password: hashedNewPassword,
            passwordUpdatedAt: new Date(),
            passwordExpiresAt,
            mustChangePassword: false,
            failedLoginAttempts: 0,
            lockUntil: undefined
        });
        // Add to password history
        await (0, passwordPolicy_1.addPasswordToHistory)(adminId, hashedNewPassword, true);
        // Log security event
        await (0, authLogger_1.logAuthEvent)(authLogger_1.AuthEventType.PASSWORD_CHANGED, 'Admin password changed successfully', {
            adminId,
            email: admin.email,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            passwordScore: passwordValidation.score
        }, authLogger_1.SecurityLevel.INFO);
        res.json({
            success: true,
            message: 'Password changed successfully',
            data: {
                passwordScore: passwordValidation.score,
                expiresAt: passwordExpiresAt.toISOString()
            }
        });
    }
    catch (error) {
        console.error('Password change error:', error);
        // Log security event
        await (0, authLogger_1.logAuthEvent)(authLogger_1.AuthEventType.PASSWORD_CHANGE_FAILED, 'Password change failed - system error', {
            adminId: (_b = req.user) === null || _b === void 0 ? void 0 : _b.id,
            error: error.message
        }, authLogger_1.SecurityLevel.ERROR);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.changePassword = changePassword;
/**
 * Verify authentication status
 */
const verifyAuth = async (req, res) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Not authenticated'
            });
        }
        res.json({
            success: true,
            message: 'Authentication verified',
            data: {
                user: req.user
            }
        });
    }
    catch (error) {
        console.error('Verify auth error:', error);
        res.status(500).json({
            success: false,
            message: 'Authentication verification failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};
exports.verifyAuth = verifyAuth;
