"use strict";
/**
 * Authentication Logger
 *
 * Specialized logger for authentication-related events with enhanced security monitoring
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logSuspiciousActivity = exports.logAccountLocked = exports.logLoginFailure = exports.logLoginSuccess = exports.logAuthEvent = exports.SecurityLevel = exports.AuthEventType = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Ensure logs directory exists
const logDir = path_1.default.join(__dirname, '../../logs/auth');
if (!fs_1.default.existsSync(logDir)) {
    fs_1.default.mkdirSync(logDir, { recursive: true });
}
// Define log formats
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.printf((info) => `${info.timestamp} ${info.level}: [AUTH] ${info.message}${info.data ? ' ' + JSON.stringify(info.data) : ''}`));
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.json());
// Create the auth logger
const authLogger = winston_1.default.createLogger({
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    defaultMeta: { service: 'mabourse-auth' },
    transports: [
        // Console transport
        new winston_1.default.transports.Console({
            format: consoleFormat,
        }),
        // Auth log file transport
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'auth.log'),
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 10,
        }),
        // Security events log file transport
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'security.log'),
            level: 'warn',
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 10,
        }),
    ],
});
// Define auth event types
var AuthEventType;
(function (AuthEventType) {
    AuthEventType["LOGIN_SUCCESS"] = "LOGIN_SUCCESS";
    AuthEventType["LOGIN_FAILURE"] = "LOGIN_FAILURE";
    AuthEventType["LOGIN_FAILED"] = "LOGIN_FAILED";
    AuthEventType["LOGOUT"] = "LOGOUT";
    AuthEventType["PASSWORD_RESET_REQUEST"] = "PASSWORD_RESET_REQUEST";
    AuthEventType["PASSWORD_RESET_SUCCESS"] = "PASSWORD_RESET_SUCCESS";
    AuthEventType["PASSWORD_RESET_FAILURE"] = "PASSWORD_RESET_FAILURE";
    AuthEventType["PASSWORD_RESET"] = "PASSWORD_RESET";
    AuthEventType["PASSWORD_RESET_FAILED"] = "PASSWORD_RESET_FAILED";
    AuthEventType["PASSWORD_CHANGED"] = "PASSWORD_CHANGED";
    AuthEventType["PASSWORD_CHANGE_REQUIRED"] = "PASSWORD_CHANGE_REQUIRED";
    AuthEventType["PASSWORD_CHANGE_FAILED"] = "PASSWORD_CHANGE_FAILED";
    AuthEventType["PASSWORD_EXPIRED"] = "PASSWORD_EXPIRED";
    AuthEventType["ACCOUNT_LOCKED"] = "ACCOUNT_LOCKED";
    AuthEventType["ACCOUNT_UNLOCKED"] = "ACCOUNT_UNLOCKED";
    AuthEventType["ADMIN_CREATED"] = "ADMIN_CREATED";
    AuthEventType["ADMIN_CREATION_FAILED"] = "ADMIN_CREATION_FAILED";
    AuthEventType["ADMIN_SETUP_FAILED"] = "ADMIN_SETUP_FAILED";
    AuthEventType["TWO_FACTOR_ENABLED"] = "TWO_FACTOR_ENABLED";
    AuthEventType["TWO_FACTOR_DISABLED"] = "TWO_FACTOR_DISABLED";
    AuthEventType["TWO_FACTOR_SUCCESS"] = "TWO_FACTOR_SUCCESS";
    AuthEventType["TWO_FACTOR_FAILURE"] = "TWO_FACTOR_FAILURE";
    AuthEventType["SUSPICIOUS_ACTIVITY"] = "SUSPICIOUS_ACTIVITY";
    AuthEventType["PERMISSION_DENIED"] = "PERMISSION_DENIED";
})(AuthEventType || (exports.AuthEventType = AuthEventType = {}));
// Define security levels
var SecurityLevel;
(function (SecurityLevel) {
    SecurityLevel["INFO"] = "info";
    SecurityLevel["WARN"] = "warn";
    SecurityLevel["WARNING"] = "warn";
    SecurityLevel["ERROR"] = "error";
    SecurityLevel["ALERT"] = "alert";
    SecurityLevel["CRITICAL"] = "critical";
})(SecurityLevel || (exports.SecurityLevel = SecurityLevel = {}));
// Define log functions
const logAuthEvent = (eventType, message, data, securityLevel = SecurityLevel.INFO) => {
    // Sanitize sensitive data
    const sanitizedData = { ...data };
    if (sanitizedData === null || sanitizedData === void 0 ? void 0 : sanitizedData.password)
        sanitizedData.password = '[REDACTED]';
    if (sanitizedData === null || sanitizedData === void 0 ? void 0 : sanitizedData.token)
        sanitizedData.token = '[REDACTED]';
    if (sanitizedData === null || sanitizedData === void 0 ? void 0 : sanitizedData.resetToken)
        sanitizedData.resetToken = '[REDACTED]';
    if (sanitizedData === null || sanitizedData === void 0 ? void 0 : sanitizedData.twoFactorSecret)
        sanitizedData.twoFactorSecret = '[REDACTED]';
    // Add event type to data
    sanitizedData.eventType = eventType;
    // Log based on security level
    switch (securityLevel) {
        case SecurityLevel.INFO:
            authLogger.info(message, { data: sanitizedData });
            break;
        case SecurityLevel.WARN:
            authLogger.warn(message, { data: sanitizedData });
            break;
        case SecurityLevel.ALERT:
            authLogger.error(message, { data: sanitizedData });
            break;
        case SecurityLevel.CRITICAL:
            authLogger.error(`[CRITICAL] ${message}`, { data: sanitizedData });
            // Could trigger additional alerts here (email, SMS, etc.)
            break;
    }
};
exports.logAuthEvent = logAuthEvent;
// Specialized logging functions
const logLoginSuccess = (userId, email, role, ip, userAgent) => {
    (0, exports.logAuthEvent)(AuthEventType.LOGIN_SUCCESS, `User logged in successfully: ${email}`, { userId, email, role, ip, userAgent }, SecurityLevel.INFO);
};
exports.logLoginSuccess = logLoginSuccess;
const logLoginFailure = (email, reason, ip, failedAttempts, userAgent) => {
    const securityLevel = failedAttempts >= 3 ? SecurityLevel.WARN : SecurityLevel.INFO;
    (0, exports.logAuthEvent)(AuthEventType.LOGIN_FAILURE, `Login failed for ${email}: ${reason}`, { email, reason, ip, failedAttempts, userAgent }, securityLevel);
};
exports.logLoginFailure = logLoginFailure;
const logAccountLocked = (userId, email, ip) => {
    (0, exports.logAuthEvent)(AuthEventType.ACCOUNT_LOCKED, `Account locked: ${email}`, { userId, email, ip }, SecurityLevel.ALERT);
};
exports.logAccountLocked = logAccountLocked;
const logSuspiciousActivity = (userId, email, activity, ip, details) => {
    (0, exports.logAuthEvent)(AuthEventType.SUSPICIOUS_ACTIVITY, `Suspicious activity detected: ${activity}`, { userId, email, activity, ip, details }, SecurityLevel.ALERT);
};
exports.logSuspiciousActivity = logSuspiciousActivity;
exports.default = {
    logAuthEvent: exports.logAuthEvent,
    logLoginSuccess: exports.logLoginSuccess,
    logLoginFailure: exports.logLoginFailure,
    logAccountLocked: exports.logAccountLocked,
    logSuspiciousActivity: exports.logSuspiciousActivity,
    AuthEventType,
    SecurityLevel,
};
